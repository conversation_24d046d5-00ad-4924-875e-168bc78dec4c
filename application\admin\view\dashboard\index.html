<style type="text/css">
    /* 现代化控制台样式 */
    .dashboard-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .dashboard-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .dashboard-header h3 {
        color: #2d3748;
        font-weight: 700;
        margin: 0;
        font-size: 28px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* 统计卡片样式 */
    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--card-color, #667eea), var(--card-color-end, #764ba2));
    }

    .stat-card.primary { --card-color: #4facfe; --card-color-end: #00f2fe; }
    .stat-card.success { --card-color: #43e97b; --card-color-end: #38f9d7; }
    .stat-card.warning { --card-color: #fa709a; --card-color-end: #fee140; }
    .stat-card.danger { --card-color: #ff6b6b; --card-color-end: #ffa726; }
    .stat-card.info { --card-color: #667eea; --card-color-end: #764ba2; }
    .stat-card.purple { --card-color: #a8edea; --card-color-end: #fed6e3; }

    .stat-icon {
        width: 70px;
        height: 70px;
        border-radius: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        margin-bottom: 15px;
        background: linear-gradient(135deg, var(--card-color, #667eea), var(--card-color-end, #764ba2));
        color: white;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .stat-content {
        flex: 1;
    }

    .stat-value {
        font-size: 32px;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 5px;
        line-height: 1.2;
    }

    .stat-label {
        color: #718096;
        font-size: 14px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stat-trend {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(67, 233, 123, 0.1);
        color: #43e97b;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
    }

    /* 面板样式 */
    .modern-panel {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 30px;
        overflow: hidden;
    }

    .modern-panel-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 20px 25px;
        border-bottom: none;
    }

    .modern-panel-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    .modern-panel-body {
        padding: 25px;
    }

    /* 表格样式 */
    .modern-table {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .modern-table thead {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    }

    .modern-table th {
        border: none;
        padding: 15px;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .modern-table td {
        border: none;
        padding: 15px;
        border-bottom: 1px solid #f1f3f4;
        color: #2d3748;
        font-weight: 500;
    }

    .modern-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 10px;
        }

        .stat-card {
            margin-bottom: 15px;
        }

        .stat-value {
            font-size: 24px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            font-size: 28px;
        }
    }

    /* 动画效果 */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .stat-card {
        animation: fadeInUp 0.6s ease forwards;
    }

    .stat-card:nth-child(1) { animation-delay: 0.1s; }
    .stat-card:nth-child(2) { animation-delay: 0.2s; }
    .stat-card:nth-child(3) { animation-delay: 0.3s; }
    .stat-card:nth-child(4) { animation-delay: 0.4s; }
</style>
<div class="dashboard-container">
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="dashboard-header">
            <h3>📊 数据控制台</h3>
            <p style="margin: 10px 0 0 0; color: #718096;">实时监控系统运营数据</p>
        </div>

        <!-- 总体数据统计 -->
        <div class="modern-panel">
            <div class="modern-panel-header">
                <h3>💼 总体数据统计</h3>
            </div>
            <div class="modern-panel-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card success">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">💰</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$today_amount}</div>
                                    <div class="stat-label">今日充值总额</div>
                                </div>
                            </div>
                            <div class="stat-trend">+12.5%</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card info">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">�</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$recharge_ratio}</div>
                                    <div class="stat-label">今日总成功率</div>
                                </div>
                            </div>
                            <div class="stat-trend">+2.1%</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card primary">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">✅</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$normal_count}</div>
                                    <div class="stat-label">正常账号总数</div>
                                </div>
                            </div>
                            <div class="stat-trend">稳定</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card danger">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">⚠️</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$abnormal_count}</div>
                                    <div class="stat-label">异常账号总数</div>
                                </div>
                            </div>
                            <div class="stat-trend" style="background: rgba(255, 107, 107, 0.1); color: #ff6b6b;">需关注</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支付宝点单码数据 -->
        <div class="modern-panel">
            <div class="modern-panel-header">
                <h3>📱 支付宝点单码数据</h3>
            </div>
            <div class="modern-panel-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card warning">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">�</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$diandan_amount}</div>
                                    <div class="stat-label">今日点单码充值</div>
                                </div>
                            </div>
                            <div class="stat-trend">+8.3%</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card success">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">�</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$diandan_ratio}</div>
                                    <div class="stat-label">点单码成功率</div>
                                </div>
                            </div>
                            <div class="stat-trend">+1.2%</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card primary">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">🟢</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$diandan_normal_count}</div>
                                    <div class="stat-label">点单码正常账号</div>
                                </div>
                            </div>
                            <div class="stat-trend">稳定</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card danger">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">🔴</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$diandan_abnormal_count}</div>
                                    <div class="stat-label">点单码异常账号</div>
                                </div>
                            </div>
                            <div class="stat-trend" style="background: rgba(255, 107, 107, 0.1); color: #ff6b6b;">需处理</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支付宝商家码数据 -->
        <div class="modern-panel">
            <div class="modern-panel-header">
                <h3>🏪 支付宝商家码数据</h3>
            </div>
            <div class="modern-panel-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card purple">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">�</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$shopcode_amount}</div>
                                    <div class="stat-label">今日商家码充值</div>
                                </div>
                            </div>
                            <div class="stat-trend">+15.7%</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card info">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">�</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$shopcode_ratio}</div>
                                    <div class="stat-label">商家码成功率</div>
                                </div>
                            </div>
                            <div class="stat-trend">+3.4%</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card success">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">🟢</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$shopcode_normal_count}</div>
                                    <div class="stat-label">商家码正常账号</div>
                                </div>
                            </div>
                            <div class="stat-trend">稳定</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <div class="stat-card danger">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon">�</div>
                                <div class="stat-content">
                                    <div class="stat-value">{$shopcode_abnormal_count}</div>
                                    <div class="stat-label">商家码异常账号</div>
                                </div>
                            </div>
                            <div class="stat-trend" style="background: rgba(255, 107, 107, 0.1); color: #ff6b6b;">需处理</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 充值统计表格 -->
        <div class="row">
            <!-- 总体充值统计 -->
            <div class="col-lg-4 col-md-12">
                <div class="modern-panel">
                    <div class="modern-panel-header">
                        <h3>📊 总体充值统计（近7天）</h3>
                    </div>
                    <div class="modern-panel-body">
                        <div class="modern-table">
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th>📅 日期</th>
                                    <th>💰 充值金额</th>
                                </tr>
                                </thead>
                                <tbody>
                                {volist name="recharge_list" id="vo"}
                                <tr>
                                    <td>{$vo.date}</td>
                                    <td><strong style="color: #43e97b;">¥{$vo.amount}</strong></td>
                                </tr>
                                {/volist}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 支付宝点单码充值统计 -->
            <div class="col-lg-4 col-md-12">
                <div class="modern-panel">
                    <div class="modern-panel-header">
                        <h3>📱 点单码充值统计（近7天）</h3>
                    </div>
                    <div class="modern-panel-body">
                        <div class="modern-table">
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th>📅 日期</th>
                                    <th>💳 充值金额</th>
                                </tr>
                                </thead>
                                <tbody>
                                {volist name="diandan_recharge_list" id="vo"}
                                <tr>
                                    <td>{$vo.date}</td>
                                    <td><strong style="color: #fa709a;">¥{$vo.amount}</strong></td>
                                </tr>
                                {/volist}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 支付宝商家码充值统计 -->
            <div class="col-lg-4 col-md-12">
                <div class="modern-panel">
                    <div class="modern-panel-header">
                        <h3>🏪 商家码充值统计（近7天）</h3>
                    </div>
                    <div class="modern-panel-body">
                        <div class="modern-table">
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th>📅 日期</th>
                                    <th>🏬 充值金额</th>
                                </tr>
                                </thead>
                                <tbody>
                                {volist name="shopcode_recharge_list" id="vo"}
                                <tr>
                                    <td>{$vo.date}</td>
                                    <td><strong style="color: #a8edea;">¥{$vo.amount}</strong></td>
                                </tr>
                                {/volist}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
